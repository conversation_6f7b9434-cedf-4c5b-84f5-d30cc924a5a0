import sqlite3
import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class MessageDatabase:
    def __init__(self, db_path='messages.db'):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                ip_address TEXT,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'offline',
                avatar_color TEXT DEFAULT '#007bff',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Messages table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_id INTEGER NOT NULL,
                message_type TEXT DEFAULT 'text',
                content TEXT NOT NULL,
                file_path TEXT,
                file_name TEXT,
                file_size INTEGER,
                is_broadcast BOOLEAN DEFAULT FALSE,
                room_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users (id)
            )
        ''')
        
        # Message recipients table (for private messages)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS message_recipients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                message_id INTEGER NOT NULL,
                recipient_id INTEGER NOT NULL,
                read_at TIMESTAMP,
                FOREIGN KEY (message_id) REFERENCES messages (id),
                FOREIGN KEY (recipient_id) REFERENCES users (id)
            )
        ''')
        
        # Active sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS active_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_id TEXT UNIQUE NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_user(self, username: str, display_name: str, ip_address: str = None) -> int:
        """Create a new user or update existing one"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO users (username, display_name, ip_address, last_seen, status)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP, 'online')
            ''', (username, display_name, ip_address))
            
            user_id = cursor.lastrowid
            conn.commit()
            return user_id
        except Exception as e:
            print(f"Error creating user: {e}")
            return None
        finally:
            conn.close()
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """Get user by username"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return {
                'id': row[0], 'username': row[1], 'display_name': row[2],
                'ip_address': row[3], 'last_seen': row[4], 'status': row[5],
                'avatar_color': row[6], 'created_at': row[7]
            }
        return None
    
    def get_all_users(self) -> List[Dict]:
        """Get all users"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users ORDER BY last_seen DESC')
        rows = cursor.fetchall()
        conn.close()
        
        users = []
        for row in rows:
            users.append({
                'id': row[0], 'username': row[1], 'display_name': row[2],
                'ip_address': row[3], 'last_seen': row[4], 'status': row[5],
                'avatar_color': row[6], 'created_at': row[7]
            })
        return users
    
    def update_user_status(self, user_id: int, status: str):
        """Update user status"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users SET status = ?, last_seen = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (status, user_id))
        
        conn.commit()
        conn.close()
    
    def save_message(self, sender_id: int, content: str, message_type: str = 'text', 
                    file_path: str = None, file_name: str = None, file_size: int = None,
                    is_broadcast: bool = False, room_id: str = None) -> int:
        """Save a message to the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO messages (sender_id, message_type, content, file_path, file_name, 
                                file_size, is_broadcast, room_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (sender_id, message_type, content, file_path, file_name, file_size, is_broadcast, room_id))
        
        message_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return message_id
    
    def get_recent_messages(self, limit: int = 50, room_id: str = None) -> List[Dict]:
        """Get recent messages"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if room_id:
            cursor.execute('''
                SELECT m.*, u.username, u.display_name, u.avatar_color
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.room_id = ? OR m.is_broadcast = TRUE
                ORDER BY m.timestamp DESC
                LIMIT ?
            ''', (room_id, limit))
        else:
            cursor.execute('''
                SELECT m.*, u.username, u.display_name, u.avatar_color
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.is_broadcast = TRUE
                ORDER BY m.timestamp DESC
                LIMIT ?
            ''', (limit,))
        
        rows = cursor.fetchall()
        conn.close()
        
        messages = []
        for row in rows:
            messages.append({
                'id': row[0], 'sender_id': row[1], 'message_type': row[2],
                'content': row[3], 'file_path': row[4], 'file_name': row[5],
                'file_size': row[6], 'is_broadcast': row[7], 'room_id': row[8],
                'timestamp': row[9], 'username': row[10], 'display_name': row[11],
                'avatar_color': row[12]
            })
        
        return list(reversed(messages))  # Return in chronological order
    
    def create_session(self, user_id: int, session_id: str, ip_address: str, user_agent: str):
        """Create a new session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO active_sessions (user_id, session_id, ip_address, user_agent)
            VALUES (?, ?, ?, ?)
        ''', (user_id, session_id, ip_address, user_agent))
        
        conn.commit()
        conn.close()
    
    def remove_session(self, session_id: str):
        """Remove a session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM active_sessions WHERE session_id = ?', (session_id,))
        conn.commit()
        conn.close()
    
    def get_online_users(self) -> List[Dict]:
        """Get currently online users"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT DISTINCT u.* FROM users u
            JOIN active_sessions s ON u.id = s.user_id
            WHERE datetime(s.last_activity) > datetime('now', '-5 minutes')
        ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        users = []
        for row in rows:
            users.append({
                'id': row[0], 'username': row[1], 'display_name': row[2],
                'ip_address': row[3], 'last_seen': row[4], 'status': row[5],
                'avatar_color': row[6], 'created_at': row[7]
            })
        return users
