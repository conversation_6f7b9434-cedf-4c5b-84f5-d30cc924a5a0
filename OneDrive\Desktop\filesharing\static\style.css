/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.header-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Cards */
.card, .status-card, .actions-card, .qr-card, .upload-card, .info-card, 
.instructions-card, .files-card, .breadcrumb-card, .connect-card, 
.quick-connect-card, .recent-card, .scanner-card, .tips-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover, .status-card:hover, .actions-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

/* Status Card */
.status-info {
    display: grid;
    gap: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.ip-address, .port, .server-url {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
}

.server-url a {
    color: #007bff;
    text-decoration: none;
}

.server-url a:hover {
    text-decoration: underline;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-download {
    background: #fd7e14;
    color: white;
    padding: 8px 16px;
    font-size: 12px;
}

.btn-download:hover {
    background: #e55a00;
}

.btn-open {
    background: #20c997;
    color: white;
    padding: 8px 16px;
    font-size: 12px;
}

.btn-open:hover {
    background: #17a085;
}

/* Forms */
.folder-form, .connect-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.input-group {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.input-group label {
    font-weight: 600;
    min-width: 100px;
}

.input-group input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    min-width: 200px;
}

.input-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

/* QR Code */
.qr-container {
    text-align: center;
    margin: 20px 0;
}

.qr-code {
    max-width: 200px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
}

.qr-url {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    word-break: break-all;
}

/* Upload Area */
.upload-area {
    border: 3px dashed #007bff;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover, .upload-area.dragover {
    border-color: #0056b3;
    background: #e6f3ff;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    display: none;
}

.upload-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.upload-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

/* Flash Messages */
.flash-messages {
    margin-bottom: 20px;
}

.flash-message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    font-weight: 500;
}

.flash-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.flash-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* File Browser */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.breadcrumb-item {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.breadcrumb-item:hover {
    background: #e9ecef;
}

.breadcrumb-separator {
    color: #6c757d;
}

.files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.files-count {
    background: #e9ecef;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.file-list {
    display: grid;
    gap: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.file-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 15px;
    min-width: 30px;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    margin-bottom: 5px;
    word-break: break-word;
}

.file-name a {
    color: #007bff;
    text-decoration: none;
}

.file-name a:hover {
    text-decoration: underline;
}

.file-size {
    font-size: 12px;
    color: #6c757d;
}

.file-actions {
    margin-left: 15px;
}

.empty-folder {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

/* Instructions */
.instructions {
    display: grid;
    gap: 15px;
}

.instruction-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.instruction-step {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.step-number {
    background: #007bff;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

/* Connect Page Specific */
.url-input-group {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.url-input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
}

.recent-list {
    display: grid;
    gap: 10px;
}

.recent-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.recent-info strong {
    display: block;
    font-family: 'Courier New', monospace;
}

.recent-info small {
    color: #6c757d;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

.scan-results {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    min-height: 50px;
}

.tips-list {
    list-style: none;
    display: grid;
    gap: 10px;
}

.tips-list li {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .input-group label {
        min-width: auto;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .file-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .file-actions {
        margin-left: 0;
        text-align: center;
    }
    
    .quick-actions {
        flex-direction: column;
    }
}
