from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room, leave_room, disconnect
from flask import request, session
import uuid
import json
from datetime import datetime
from database import MessageDatabase
import os
from werkzeug.utils import secure_filename

class MessagingSystem:
    def __init__(self, app, socketio):
        self.app = app
        self.socketio = socketio
        self.db = MessageDatabase()
        self.connected_users = {}  # session_id -> user_info
        self.user_sessions = {}    # user_id -> [session_ids]
        
        # Register SocketIO event handlers
        self.register_handlers()
    
    def register_handlers(self):
        """Register all SocketIO event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            print(f"Client connected: {request.sid}")
            
        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client disconnected: {request.sid}")
            self.handle_user_disconnect(request.sid)
            
        @self.socketio.on('join_chat')
        def handle_join_chat(data):
            username = data.get('username', f'User_{request.sid[:8]}')
            display_name = data.get('display_name', username)
            ip_address = request.environ.get('REMOTE_ADDR')
            
            # Create or get user
            user = self.db.get_user_by_username(username)
            if not user:
                user_id = self.db.create_user(username, display_name, ip_address)
                user = self.db.get_user_by_username(username)
            else:
                user_id = user['id']
                self.db.update_user_status(user_id, 'online')
            
            # Store session info
            self.connected_users[request.sid] = {
                'user_id': user_id,
                'username': username,
                'display_name': display_name,
                'ip_address': ip_address
            }
            
            # Track user sessions
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = []
            self.user_sessions[user_id].append(request.sid)
            
            # Create database session
            self.db.create_session(user_id, request.sid, ip_address, 
                                 request.headers.get('User-Agent', ''))
            
            # Join general room
            join_room('general')
            
            # Send recent messages
            recent_messages = self.db.get_recent_messages(limit=50)
            emit('message_history', {'messages': recent_messages})
            
            # Notify others of new user
            self.broadcast_user_list()
            
            # Send welcome message
            emit('system_message', {
                'type': 'success',
                'message': f'Welcome to the chat, {display_name}!'
            })
            
            print(f"User {username} joined chat")
            
        @self.socketio.on('send_message')
        def handle_send_message(data):
            if request.sid not in self.connected_users:
                emit('error', {'message': 'Not authenticated'})
                return
            
            user_info = self.connected_users[request.sid]
            message_content = data.get('message', '').strip()
            message_type = data.get('type', 'text')
            is_broadcast = data.get('broadcast', True)
            
            if not message_content:
                return
            
            # Save message to database
            message_id = self.db.save_message(
                sender_id=user_info['user_id'],
                content=message_content,
                message_type=message_type,
                is_broadcast=is_broadcast
            )
            
            # Create message object
            message_obj = {
                'id': message_id,
                'username': user_info['username'],
                'display_name': user_info['display_name'],
                'content': message_content,
                'type': message_type,
                'timestamp': datetime.now().isoformat(),
                'avatar_color': self.get_user_avatar_color(user_info['user_id'])
            }
            
            # Broadcast message
            if is_broadcast:
                self.socketio.emit('new_message', message_obj, room='general')
            
            print(f"Message from {user_info['username']}: {message_content}")
            
        @self.socketio.on('send_file_message')
        def handle_send_file_message(data):
            if request.sid not in self.connected_users:
                emit('error', {'message': 'Not authenticated'})
                return
            
            user_info = self.connected_users[request.sid]
            file_info = data.get('file_info', {})
            message_content = data.get('message', f"Shared file: {file_info.get('name', 'Unknown')}")
            
            # Save file message to database
            message_id = self.db.save_message(
                sender_id=user_info['user_id'],
                content=message_content,
                message_type='file',
                file_path=file_info.get('path'),
                file_name=file_info.get('name'),
                file_size=file_info.get('size'),
                is_broadcast=True
            )
            
            # Create message object
            message_obj = {
                'id': message_id,
                'username': user_info['username'],
                'display_name': user_info['display_name'],
                'content': message_content,
                'type': 'file',
                'file_info': file_info,
                'timestamp': datetime.now().isoformat(),
                'avatar_color': self.get_user_avatar_color(user_info['user_id'])
            }
            
            # Broadcast file message
            self.socketio.emit('new_message', message_obj, room='general')
            
        @self.socketio.on('broadcast_message')
        def handle_broadcast_message(data):
            if request.sid not in self.connected_users:
                emit('error', {'message': 'Not authenticated'})
                return
            
            user_info = self.connected_users[request.sid]
            message_content = data.get('message', '').strip()
            
            if not message_content:
                return
            
            # Save broadcast message
            message_id = self.db.save_message(
                sender_id=user_info['user_id'],
                content=f"📢 BROADCAST: {message_content}",
                message_type='broadcast',
                is_broadcast=True
            )
            
            # Create broadcast message object
            message_obj = {
                'id': message_id,
                'username': user_info['username'],
                'display_name': user_info['display_name'],
                'content': f"📢 {message_content}",
                'type': 'broadcast',
                'timestamp': datetime.now().isoformat(),
                'avatar_color': '#ff6b35'  # Special color for broadcasts
            }
            
            # Broadcast to all users
            self.socketio.emit('new_message', message_obj)
            
        @self.socketio.on('typing_start')
        def handle_typing_start():
            if request.sid in self.connected_users:
                user_info = self.connected_users[request.sid]
                self.socketio.emit('user_typing', {
                    'username': user_info['username'],
                    'display_name': user_info['display_name'],
                    'typing': True
                }, room='general', include_self=False)
                
        @self.socketio.on('typing_stop')
        def handle_typing_stop():
            if request.sid in self.connected_users:
                user_info = self.connected_users[request.sid]
                self.socketio.emit('user_typing', {
                    'username': user_info['username'],
                    'display_name': user_info['display_name'],
                    'typing': False
                }, room='general', include_self=False)
                
        @self.socketio.on('request_user_list')
        def handle_request_user_list():
            self.send_user_list_to_client(request.sid)
    
    def handle_user_disconnect(self, session_id):
        """Handle user disconnection"""
        if session_id in self.connected_users:
            user_info = self.connected_users[session_id]
            user_id = user_info['user_id']
            
            # Remove from connected users
            del self.connected_users[session_id]
            
            # Remove from user sessions
            if user_id in self.user_sessions:
                if session_id in self.user_sessions[user_id]:
                    self.user_sessions[user_id].remove(session_id)
                
                # If no more sessions, mark user as offline
                if not self.user_sessions[user_id]:
                    self.db.update_user_status(user_id, 'offline')
                    del self.user_sessions[user_id]
            
            # Remove database session
            self.db.remove_session(session_id)
            
            # Update user list
            self.broadcast_user_list()
            
            leave_room('general', sid=session_id)
    
    def broadcast_user_list(self):
        """Broadcast updated user list to all clients"""
        online_users = self.get_online_users()
        self.socketio.emit('user_list_update', {'users': online_users}, room='general')
    
    def send_user_list_to_client(self, session_id):
        """Send user list to specific client"""
        online_users = self.get_online_users()
        self.socketio.emit('user_list_update', {'users': online_users}, room=session_id)
    
    def get_online_users(self):
        """Get list of currently online users"""
        online_users = []
        for session_id, user_info in self.connected_users.items():
            online_users.append({
                'username': user_info['username'],
                'display_name': user_info['display_name'],
                'status': 'online',
                'avatar_color': self.get_user_avatar_color(user_info['user_id'])
            })
        return online_users
    
    def get_user_avatar_color(self, user_id):
        """Get user's avatar color"""
        user = self.db.get_user_by_username(self.get_username_by_id(user_id))
        return user.get('avatar_color', '#007bff') if user else '#007bff'
    
    def get_username_by_id(self, user_id):
        """Get username by user ID"""
        for session_id, user_info in self.connected_users.items():
            if user_info['user_id'] == user_id:
                return user_info['username']
        return None
    
    def send_system_message(self, message, message_type='info'):
        """Send system message to all users"""
        message_obj = {
            'id': f'system_{datetime.now().timestamp()}',
            'username': 'System',
            'display_name': 'System',
            'content': message,
            'type': 'system',
            'timestamp': datetime.now().isoformat(),
            'avatar_color': '#6c757d'
        }
        
        self.socketio.emit('new_message', message_obj, room='general')
