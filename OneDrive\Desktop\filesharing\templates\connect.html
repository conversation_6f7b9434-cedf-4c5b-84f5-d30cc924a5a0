<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect to Others - LAN File Sharing</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔗 Connect to Others</h1>
            <p>Access files shared by others on your network</p>
            <div class="header-actions">
                <a href="{{ url_for('index') }}" class="btn btn-secondary">🏠 Home</a>
                <a href="{{ url_for('browse') }}" class="btn btn-success">📂 My Files</a>
            </div>
        </header>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Connection Form -->
        <div class="connect-card">
            <h2>🌐 Connect to Remote Server</h2>
            <form method="POST" action="{{ url_for('connect_to') }}" class="connect-form">
                <div class="input-group">
                    <label for="remote_ip">IP Address:</label>
                    <input type="text" id="remote_ip" name="remote_ip" 
                           placeholder="e.g., ***********" 
                           pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$"
                           required>
                </div>
                
                <div class="input-group">
                    <label for="remote_port">Port:</label>
                    <input type="number" id="remote_port" name="remote_port" 
                           placeholder="8080" 
                           value="8080"
                           min="1" max="65535"
                           required>
                </div>
                
                <button type="submit" class="btn btn-primary btn-connect">
                    🔗 Connect
                </button>
            </form>
        </div>

        <!-- Quick Connect -->
        <div class="quick-connect-card">
            <h2>⚡ Quick Connect</h2>
            <p>Enter the full URL if you have it:</p>
            <div class="url-input-group">
                <input type="text" id="quickUrl" placeholder="http://***********:8080" class="url-input">
                <button onclick="quickConnect()" class="btn btn-info">Go</button>
            </div>
        </div>

        <!-- Recent Connections -->
        <div class="recent-card">
            <h2>📋 Recent Connections</h2>
            <div id="recentConnections" class="recent-list">
                <p class="no-recent">No recent connections</p>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions-card">
            <h2>📖 How to Connect</h2>
            <div class="instructions">
                <div class="instruction-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <strong>Get the IP and Port</strong>
                        <p>Ask the person sharing files for their IP address and port number</p>
                    </div>
                </div>
                
                <div class="instruction-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <strong>Enter Connection Details</strong>
                        <p>Fill in the IP address and port in the form above</p>
                    </div>
                </div>
                
                <div class="instruction-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <strong>Connect and Browse</strong>
                        <p>Click connect to access their shared files</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Network Scanner -->
        <div class="scanner-card">
            <h2>🔍 Network Scanner</h2>
            <p>Scan your local network for active file sharing servers:</p>
            <button onclick="scanNetwork()" class="btn btn-scan" id="scanBtn">
                🔍 Scan Network
            </button>
            <div id="scanResults" class="scan-results"></div>
        </div>

        <!-- Tips -->
        <div class="tips-card">
            <h2>💡 Tips</h2>
            <ul class="tips-list">
                <li>Make sure you're on the same network (WiFi/LAN) as the person sharing</li>
                <li>The IP address usually starts with 192.168. or 10.0.</li>
                <li>Common ports are 8080, 8000, 5000</li>
                <li>If connection fails, check if their server is running</li>
            </ul>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        function quickConnect() {
            const url = document.getElementById('quickUrl').value.trim();
            if (url) {
                // Extract IP and port from URL
                try {
                    const urlObj = new URL(url);
                    const ip = urlObj.hostname;
                    const port = urlObj.port || '8080';
                    
                    document.getElementById('remote_ip').value = ip;
                    document.getElementById('remote_port').value = port;
                    
                    // Open in new tab
                    window.open(url + '/browse', '_blank');
                } catch (e) {
                    alert('Invalid URL format. Please use format: http://***********:8080');
                }
            }
        }

        function scanNetwork() {
            const scanBtn = document.getElementById('scanBtn');
            const scanResults = document.getElementById('scanResults');
            
            scanBtn.disabled = true;
            scanBtn.textContent = '🔍 Scanning...';
            scanResults.innerHTML = '<p>Scanning network for active servers...</p>';
            
            // This is a placeholder - actual network scanning would require backend implementation
            setTimeout(() => {
                scanResults.innerHTML = `
                    <p>Network scan completed. Found 0 active file sharing servers.</p>
                    <p><small>Note: Network scanning requires additional implementation for security reasons.</small></p>
                `;
                scanBtn.disabled = false;
                scanBtn.textContent = '🔍 Scan Network';
            }, 3000);
        }

        // Load recent connections from localStorage
        function loadRecentConnections() {
            const recent = JSON.parse(localStorage.getItem('recentConnections') || '[]');
            const container = document.getElementById('recentConnections');
            
            if (recent.length === 0) {
                container.innerHTML = '<p class="no-recent">No recent connections</p>';
                return;
            }
            
            container.innerHTML = recent.map(conn => `
                <div class="recent-item">
                    <div class="recent-info">
                        <strong>${conn.ip}:${conn.port}</strong>
                        <small>${new Date(conn.timestamp).toLocaleString()}</small>
                    </div>
                    <button onclick="connectToRecent('${conn.ip}', '${conn.port}')" class="btn btn-small">Connect</button>
                </div>
            `).join('');
        }

        function connectToRecent(ip, port) {
            document.getElementById('remote_ip').value = ip;
            document.getElementById('remote_port').value = port;
        }

        // Save connection to recent
        function saveRecentConnection(ip, port) {
            const recent = JSON.parse(localStorage.getItem('recentConnections') || '[]');
            const newConn = { ip, port, timestamp: Date.now() };
            
            // Remove if already exists
            const filtered = recent.filter(conn => !(conn.ip === ip && conn.port === port));
            
            // Add to beginning
            filtered.unshift(newConn);
            
            // Keep only last 5
            const limited = filtered.slice(0, 5);
            
            localStorage.setItem('recentConnections', JSON.stringify(limited));
            loadRecentConnections();
        }

        // Load recent connections on page load
        document.addEventListener('DOMContentLoaded', loadRecentConnections);
    </script>
</body>
</html>
